package com.eastking.controller;

import com.eastking.enums.DispatchRepairTypeEnum;
import com.eastking.enums.DispatchStatusEnum;
import com.eastking.model.dto.request.DispatchOrderFilterRequest;
import com.eastking.model.dto.request.DispatchRepairFilterRequest;
import com.eastking.model.dto.request.DispatchRepairRequestDto;
import com.eastking.model.dto.request.CompleteContactRequestDto;
import com.eastking.model.dto.request.UpdateScheduleRequestDto;
import com.eastking.model.dto.request.CompleteWorkingRequestDto;
import com.eastking.model.dto.request.CompletePaymentRequestDto;
import com.eastking.model.dto.request.CompleteSignatureRequestDto;
import com.eastking.model.dto.request.SubmitHandlingRequestDto;
import com.eastking.model.dto.response.*;
import com.eastking.model.vo.ApiResponse;
import com.eastking.security.UserDetailsImpl;
import com.eastking.service.DispatchRepairService;
import com.eastking.aop.AuditAction;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import com.eastking.model.dto.request.DispatchCollaboratorRequest;
import com.eastking.model.dto.request.TransferDispatchRequest;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;
import java.util.Map;

@RestController
@RequestMapping("/api/v1")
@RequiredArgsConstructor
public class DispatchRepairController {

    private static final Logger logger = LoggerFactory.getLogger(DispatchRepairController.class);

    private final DispatchRepairService dispatchRepairService;

    // --- Adapter for old Dispatch Order routes ---

    @GetMapping("/dispatch-orders/technician-view")
    @PreAuthorize("hasAnyAuthority('F05010_READ', 'SYS_ADMIN')")
    @Operation(summary = "查詢技師視角的派工單列表")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<DispatchOrderSummaryDto>>> searchDispatchOrdersForTechnicianView(
            DispatchOrderFilterRequest filter, 
            Pageable pageable) {
        Page<DispatchRepairSummaryDto> resultPage = dispatchRepairService.searchForTechnicianView(filter, pageable);
        Page<DispatchOrderSummaryDto> adaptedPage = resultPage.map(this::adaptToDispatchOrderSummaryDto);
        return ResponseEntity.ok(ApiResponse.success(ApiResponse.PageData.fromPage(adaptedPage)));
    }

    @GetMapping("/dispatch-orders")
    @PreAuthorize("hasAnyAuthority('F05010_READ', 'SYS_ADMIN')")
    @Operation(summary = "查詢派工單管理列表")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<DispatchOrderSummaryDto>>> searchDispatchOrders(
            @RequestParam(required = false) Short dispatchTypeCode,
            @RequestParam(required = false) Short dispatchStatusCode,
            @RequestParam(required = false) UUID technicianId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String number, // For keyword search on number
            Pageable pageable) {
        
        DispatchRepairFilterRequest newFilter = new DispatchRepairFilterRequest();
        newFilter.setTypeCode(dispatchTypeCode);
        newFilter.setStatusCode(dispatchStatusCode);
        newFilter.setTechnicianId(technicianId);
        // Date parsing would be needed here if you pass string dates
        // newFilter.setStartDate(startDate); 
        // newFilter.setEndDate(endDate);
        newFilter.setNumber(number);
        
        Page<DispatchRepairSummaryDto> resultPage = dispatchRepairService.search(newFilter, pageable);

        Page<DispatchOrderSummaryDto> oldDtoPage = resultPage.map(this::adaptToDispatchOrderSummaryDto);
        
        return ResponseEntity.ok(ApiResponse.success(oldDtoPage));
    }

    @GetMapping("/dispatch-orders/{id}")
    @PreAuthorize("hasAnyAuthority('F05010_READ', 'SYS_ADMIN')")
    @Operation(summary = "查詢派工單詳情 (兼容舊版，現已升級為新版 DTO)")
    public ResponseEntity<DispatchRepairDetailDto> getDispatchOrderDetail(@PathVariable UUID id) {
        DispatchRepairDetailDto detailDto = dispatchRepairService.findById(id);
        return ResponseEntity.ok(detailDto);
    }

    @GetMapping("/dispatch-orders/pending")
    @PreAuthorize("hasAnyAuthority('F05010_READ', 'SYS_ADMIN')")
    @Operation(summary = "查詢待接單列表")
    public ResponseEntity<ApiResponse<List<DispatchRepairSummaryDto>>> getPendingDispatchOrders() {
        List<DispatchRepairSummaryDto> pendingOrders = dispatchRepairService.getPendingDispatchOrders();
        return ResponseEntity.ok(ApiResponse.success(pendingOrders));
    }

    @PostMapping("/dispatch-orders/accept")
    @PreAuthorize("hasAnyAuthority('F05010_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "技師接單")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.DISPATCH_REPAIR)
    public ResponseEntity<ApiResponse<Void>> acceptDispatchOrders(@RequestBody List<UUID> dispatchOrderIds, @AuthenticationPrincipal UserDetailsImpl currentUser) {
        dispatchRepairService.acceptDispatchOrders(dispatchOrderIds, currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success(null, "接單成功"));
    }

    @GetMapping("/dispatch-orders/pending-items")
    @PreAuthorize("hasAnyAuthority('F05020_READ', 'SYS_ADMIN')")
    @Operation(summary = "查詢所有待領料的派工品項")
    public ResponseEntity<ApiResponse<List<DispatchRepairItemSummaryDto>>> getPendingDispatchItems(
        @RequestParam(required = false) UUID dispatchOrderId) {
        List<DispatchRepairItemSummaryDto> items = dispatchRepairService.getPendingDispatchItems(dispatchOrderId, DispatchStatusEnum.MATERIALS_COLLECTED);
        return ResponseEntity.ok(ApiResponse.success(items));
    }

    @GetMapping("/dispatch-orders/refund-pending-items")
    @PreAuthorize("hasAnyAuthority('F05030_READ', 'SYS_ADMIN')")
    @Operation(summary = "查詢所有待退料的派工品項")
    public ResponseEntity<ApiResponse<List<DispatchRepairItemSummaryDto>>> getRefundPendingDispatchItems(
            @RequestParam(required = false) UUID dispatchOrderId) {
        List<DispatchRepairItemSummaryDto> items = dispatchRepairService.getPendingDispatchItems(dispatchOrderId, DispatchStatusEnum.REFUND_MATERIALS_BACK);
        return ResponseEntity.ok(ApiResponse.success(items));
    }

    @PostMapping("/dispatch-orders/from-order/{orderId}")
    @PreAuthorize("hasAnyAuthority('F05010_CREATE', 'SYS_ADMIN')")
    @Operation(summary = "從已核准的派工商品訂單建立技師派工單")
    @AuditAction(action = AuditActionTypeEnum.CREATE, dataType = AuditDataTypeEnum.DISPATCH_REPAIR)
    public ResponseEntity<ApiResponse<Void>> createDispatchRepairsFromOrder(@PathVariable UUID orderId) {
        dispatchRepairService.createDispatchRepairsFromOrder(orderId);
        return ResponseEntity.status(HttpStatus.CREATED).body(ApiResponse.success(null, "派工單已成功建立"));
    }

    @PostMapping("/repairs")
    @PreAuthorize("hasAnyAuthority('F06010_CREATE', 'SYS_ADMIN')")
    @Operation(summary = "新增派工維修單")
    @AuditAction(action = AuditActionTypeEnum.CREATE, dataType = AuditDataTypeEnum.DISPATCH_REPAIR)
    public ResponseEntity<ApiResponse<DispatchRepairDetailDto>> createDispatchRepair(
            @Valid @RequestBody DispatchRepairRequestDto requestDto) {
        DispatchRepairDetailDto createdOrder = dispatchRepairService.createDispatchRepair(requestDto);
        return ResponseEntity.status(HttpStatus.CREATED).body(ApiResponse.created(createdOrder));
    }

    @DeleteMapping("/repairs/{id}")
    @PreAuthorize("hasAnyAuthority('F06010_DELETE', 'SYS_ADMIN')")
    @Operation(summary = "刪除草稿狀態的派工維修單")
    @AuditAction(action = AuditActionTypeEnum.DELETE, dataType = AuditDataTypeEnum.DISPATCH_REPAIR)
    public ResponseEntity<ApiResponse<Void>> deleteDispatchRepair(@PathVariable UUID id) {
        dispatchRepairService.deleteDispatchRepair(id);
        return ResponseEntity.ok(ApiResponse.success(null, "派工單已刪除"));
    }

    @GetMapping("/repairs/{id}")
    @PreAuthorize("hasAnyAuthority('F06010_READ', 'SYS_ADMIN')")
    @Operation(summary = "查詢維修單詳情 (統一介面)")
    public ResponseEntity<DispatchRepairDetailDto> getRepairOrder(@PathVariable UUID id) {
        DispatchRepairDetailDto detailDto = dispatchRepairService.findById(id);
        return ResponseEntity.ok(detailDto);
    }

    @PostMapping("/dispatch-orders/{id}/complete-contact")
    @PreAuthorize("hasAnyAuthority('F05010_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "完成聯繫步驟")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.DISPATCH_REPAIR)
    public ResponseEntity<ApiResponse<Void>> completeContactStep(
            @PathVariable UUID id,
            @Valid @RequestBody CompleteContactRequestDto requestDto) {
        dispatchRepairService.completeContactStep(id, requestDto);
        return ResponseEntity.ok(ApiResponse.success(null, "聯繫步驟已完成"));
    }

    @PostMapping("/dispatch-orders/{id}/update-schedule")
    @PreAuthorize("hasAnyAuthority('F05010_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "更新排單步驟 (排程日期)")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.DISPATCH_REPAIR)
    public ResponseEntity<ApiResponse<Void>> updateSchedule(
            @PathVariable UUID id,
            @Valid @RequestBody UpdateScheduleRequestDto requestDto) {
        dispatchRepairService.updateSchedule(id, requestDto);
        return ResponseEntity.ok(ApiResponse.success(null, "排單已更新"));
    }

    @PostMapping("/dispatch-orders/{id}/complete-material-collection")
    @PreAuthorize("hasAnyAuthority('F05010_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "完成領料步驟")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.DISPATCH_REPAIR)
    public ResponseEntity<ApiResponse<Void>> completeMaterialCollection(@PathVariable UUID id) {
        dispatchRepairService.completeMaterialCollection(id);
        return ResponseEntity.ok(ApiResponse.success(null, "領料步驟已完成"));
    }

    @PostMapping("/dispatch-orders/{id}/complete-departure")
    @PreAuthorize("hasAnyAuthority('F05010_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "完成在途中步驟 (已到達)")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.DISPATCH_REPAIR)
    public ResponseEntity<ApiResponse<Void>> completeDeparture(@PathVariable UUID id) {
        dispatchRepairService.completeDeparture(id);
        return ResponseEntity.ok(ApiResponse.success(null, "已更新狀態為到達現場"));
    }

    @PostMapping("/dispatch-orders/{id}/complete-working")
    @PreAuthorize("hasAnyAuthority('F05010_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "完成裝修步驟")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.DISPATCH_REPAIR)
    public ResponseEntity<ApiResponse<Void>> completeWorkingStep(
            @PathVariable UUID id,
            @Valid @RequestBody CompleteWorkingRequestDto requestDto) {
        dispatchRepairService.completeWorkingStep(id, requestDto);
        return ResponseEntity.ok(ApiResponse.success(null, "裝修步驟已完成"));
    }

    @PostMapping("/dispatch-orders/{id}/complete-payment")
    @PreAuthorize("hasAnyAuthority('F05010_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "完成收款步驟")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.DISPATCH_REPAIR)
    public ResponseEntity<ApiResponse<Void>> completePaymentStep(
            @PathVariable UUID id,
            @Valid @RequestBody CompletePaymentRequestDto requestDto) {
        dispatchRepairService.completePaymentStep(id, requestDto);
        return ResponseEntity.ok(ApiResponse.success(null, "收款步驟已完成"));
    }

    @PostMapping("/dispatch-orders/{id}/complete-signature")
    @PreAuthorize("hasAnyAuthority('F05010_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "完成簽名步驟")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.DISPATCH_REPAIR)
    public ResponseEntity<ApiResponse<Void>> completeSignatureStep(
            @PathVariable UUID id,
            @Valid @RequestBody CompleteSignatureRequestDto requestDto) {
        dispatchRepairService.completeSignatureStep(id, requestDto);
        return ResponseEntity.ok(ApiResponse.success(null, "簽名步驟已完成"));
    }

    @PostMapping("/dispatch-orders/{id}/submit-handling")
    @PreAuthorize("hasAnyAuthority('F05010_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "提交處理方式")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.DISPATCH_REPAIR)
    public ResponseEntity<ApiResponse<Void>> submitHandlingStep(
            @PathVariable UUID id,
            @RequestBody SubmitHandlingRequestDto requestDto) {
        dispatchRepairService.submitHandlingStep(id, requestDto);
        return ResponseEntity.ok(ApiResponse.success(null, "處理方式已提交"));
    }

    @PostMapping("/dispatch-orders/{id}/collaborators")
    @PreAuthorize("hasAnyAuthority('F05010_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "邀請協同技師")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.DISPATCH_REPAIR)
    public ResponseEntity<ApiResponse<Void>> inviteCollaborators(
            @PathVariable("id") UUID dispatchRepairId,
            @Valid @RequestBody DispatchCollaboratorRequest request) {
        dispatchRepairService.inviteCollaborators(dispatchRepairId, request);
        return ResponseEntity.ok(ApiResponse.success(null, "協同技師邀請已發送"));
    }

    @PostMapping("/dispatch-orders/{id}/transfer")
    @PreAuthorize("hasAnyAuthority('F05010_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "轉單給其他技師，並依策略處理物料")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.DISPATCH_REPAIR)
    public ResponseEntity<ApiResponse<Void>> transferDispatchOrder(
            @PathVariable("id") UUID dispatchRepairId,
            @Valid @RequestBody TransferDispatchRequest request) {
        dispatchRepairService.transferDispatchRepair(dispatchRepairId, request.getTargetTechnicianId(), request.getReason(), request.getMaterialHandling());
        return ResponseEntity.ok(ApiResponse.success(null, "轉單成功"));
    }

    @PatchMapping("/dispatch-orders/{id}/urgent-status")
    @PreAuthorize("hasAnyAuthority('F05010_UPDATE', 'SYS_ADMIN')") // Assuming general update permission is sufficient
    @Operation(summary = "更新派工單的急單狀態")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.DISPATCH_REPAIR)
    public ResponseEntity<ApiResponse<Void>> updateUrgentStatus(
            @PathVariable("id") UUID dispatchRepairId,
            @RequestBody Map<String, Boolean> payload) {
        Boolean isUrgent = payload.get("isUrgent");
        if (isUrgent == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(HttpStatus.BAD_REQUEST.value(), "Request body must contain 'isUrgent' field."));
        }
        dispatchRepairService.updateUrgentStatus(dispatchRepairId, isUrgent);
        return ResponseEntity.ok(ApiResponse.success(null, "急單狀態已更新"));
    }

    @PostMapping("/dispatch-orders/{id}/collaborations/accept")
    @PreAuthorize("hasAnyAuthority('F05010_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "協同技師接受邀請")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.DISPATCH_REPAIR)
    public ResponseEntity<ApiResponse<Void>> acceptCollaboration(
            @PathVariable("id") UUID dispatchRepairId,
            @AuthenticationPrincipal UserDetailsImpl currentUser) {
        dispatchRepairService.acceptCollaboration(dispatchRepairId, currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success(null, "已接受協同作業"));
    }

    @PutMapping("/repairs/{id}")
    @PreAuthorize("hasAnyAuthority('F06010_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "修改派工維修單")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.DISPATCH_REPAIR)
    public ResponseEntity<ApiResponse<DispatchRepairDetailDto>> updateDispatchRepair(
            @PathVariable UUID id,
            @Valid @RequestBody DispatchRepairRequestDto requestDto) {
        DispatchRepairDetailDto updatedOrder = dispatchRepairService.updateDispatchRepair(id, requestDto);
        return ResponseEntity.ok(ApiResponse.success(updatedOrder));
    }

    // --- DTO Adapter Methods ---

    private DispatchOrderSummaryDto adaptToDispatchOrderSummaryDto(DispatchRepairSummaryDto newDto) {
        return DispatchOrderSummaryDto.builder()
                .dispatchOrderId(newDto.getDispatchRepairId())
                .dispatchOrderNumber(newDto.getDispatchRepairNumber())
                .dispatchTypeCode(newDto.getTypeCode())
                .dispatchTypeDescription(newDto.getTypeDescription())
                .dispatchStatusCode(newDto.getStatusCode())
                .dispatchStatusDescription(newDto.getStatusDescription())
                .customerName(newDto.getCustomerName())
                .scheduledDate(newDto.getScheduledDate())
                .assignedTechnicianName(newDto.getAssignedTechnicianName())
                .isUrgent(newDto.getIsUrgent())
                .build();
    }
} 