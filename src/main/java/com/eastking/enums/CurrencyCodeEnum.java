package com.eastking.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 幣別代碼枚舉
 */
@Getter
public enum CurrencyCodeEnum {
    TWD("TWD", "新台幣"),
    USD("USD", "美元"),
    JPY("JPY", "日圓"),
    EUR("EUR", "歐元");

    @JsonValue // For Jackson serialization/deserialization using the code
    private final String code;
    private final String description;

    CurrencyCodeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    // Optional: Method to get enum from code if needed for custom logic
    public static CurrencyCodeEnum fromCode(String code) {
        for (CurrencyCodeEnum currency : CurrencyCodeEnum.values()) {
            if (currency.getCode().equalsIgnoreCase(code)) {
                return currency;
            }
        }
        return null; // Or throw exception for unknown code
    }
} 