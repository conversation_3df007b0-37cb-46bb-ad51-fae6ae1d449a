console.log("AUDIT_LOG_VIEW.JS: Script start.");

document.addEventListener('DOMContentLoaded', function () {
    console.log("AUDIT_LOG_VIEW.JS: DOMContentLoaded event fired.");

    const filterForm = document.getElementById('auditLogFilterForm');
    const tableBody = document.getElementById('auditLogTableBody');
    const paginationControls = document.getElementById('paginationControls');
    const dataTypeSelect = document.getElementById('dataTypeSelect');
    const departmentSelect = document.getElementById('departmentSelect');
    const actionTypeSelect = document.getElementById('actionTypeSelect');
    
    const detailsModalEl = document.getElementById('logDetailsModal');
    let detailsModal = null;
    if(detailsModalEl) {
        try {
            detailsModal = new bootstrap.Modal(detailsModalEl);
            // console.log("AUDIT_LOG_VIEW.JS: Details modal initialized.");
        } catch(e) { console.error("AUDIT_LOG_VIEW.JS: Error initializing detailsModal", e); }
    } else {
        console.warn("AUDIT_LOG_VIEW.JS: logDetailsModal element not found.");
    }
    const logDetailsJsonCode = document.getElementById('logDetailsJson');
    // console.log("AUDIT_LOG_VIEW.JS: Initial elements obtained.", {filterForm:!!filterForm, tableBody:!!tableBody, paginationControls:!!paginationControls});

    let currentPage = 0;
    let currentSortBy = 'operationTime';
    let currentSortDir = 'DESC';

    async function fetchAuditLogs(page = 0, sortBy = 'operationTime', sortDir = 'DESC') {
        currentPage = page;
        currentSortBy = sortBy;
        currentSortDir = sortDir;
        // console.log(`AUDIT_LOG_VIEW.JS: fetchAuditLogs called. Page: ${page}, SortBy: ${sortBy}, SortDir: ${sortDir}`);

        const startTime = document.getElementById('startTime').value;
        const endTime = document.getElementById('endTime').value;
        const selectedDataType = dataTypeSelect ? dataTypeSelect.value : '';
        const departmentName = departmentSelect ? departmentSelect.value : '';
        const employeeId = document.getElementById('employeeIdFilter') ? document.getElementById('employeeIdFilter').value : '';
        const userName = document.getElementById('userNameFilter') ? document.getElementById('userNameFilter').value : '';
        const selectedActionType = actionTypeSelect ? actionTypeSelect.value : '';
        const searchText = document.getElementById('searchText') ? document.getElementById('searchText').value : '';

        let queryParams = `?page=${page}&size=15&sort=${sortBy},${sortDir}`; 
        if (startTime) queryParams += `&startTime=${new Date(startTime).toISOString()}`;
        if (endTime) queryParams += `&endTime=${new Date(endTime).toISOString()}`;
        if (selectedDataType) queryParams += `&dataTypes=${encodeURIComponent(selectedDataType)}`;
        if (departmentName) queryParams += `&departmentName=${encodeURIComponent(departmentName)}`;
        if (employeeId) queryParams += `&employeeId=${encodeURIComponent(employeeId)}`;
        if (userName) queryParams += `&userName=${encodeURIComponent(userName)}`;
        if (selectedActionType) queryParams += `&actionType=${encodeURIComponent(selectedActionType)}`;
        if (searchText) queryParams += `&searchText=${encodeURIComponent(searchText)}`;
        
        if(tableBody) tableBody.innerHTML = '<tr><td colspan="10" class="text-center">載入中...</td></tr>';
        // console.log("AUDIT_LOG_VIEW.JS: Fetching audit logs with params:", queryParams);

        try {
            const response = await window.fetchAuthenticated(`/api/v1/audit-logs${queryParams}`);
            // console.log("AUDIT_LOG_VIEW.JS: Audit logs fetch response status:", response.status);
            if (!response.ok) {
                 const errData = await response.json().catch(() => ({message: '載入操作歷程失敗'}));
                throw new Error(errData.message);
            }
            const apiResp = await response.json();
            // console.log("AUDIT_LOG_VIEW.JS: Audit logs API response data:", apiResp);
            const pageData = apiResp.data;
            
            if(tableBody) tableBody.innerHTML = '';
            if (pageData && pageData.list && pageData.list.length > 0) {
                pageData.list.forEach((log, index) => {
                    if(!tableBody) return;
                    const row = tableBody.insertRow();
                    row.insertCell().textContent = page * pageData.page.pageSize + index + 1;
                    row.insertCell().textContent = log.operationTime ? new Date(log.operationTime).toLocaleString() : 'N/A';
                    row.insertCell().textContent = log.dataType || 'N/A';
                    row.insertCell().textContent = log.entityIdStr || 'N/A';
                    row.insertCell().textContent = log.entityDescription || 'N/A';
                    // Assuming log.actionType is now an object {code: "...", description: "..."} if fetched from EnumValueDto list
                    // If it's just the code string from the DB, we might need to map it to description client-side or ensure DTO has description
                    row.insertCell().textContent = log.actionType ? ( (typeof log.actionType === 'object' && log.actionType.description) ? `${log.actionType.description} (${log.actionType.code})` : log.actionType ) : 'N/A';
                    row.insertCell().textContent = log.userDepartmentName || 'N/A';
                    row.insertCell().textContent = `${log.userName || ''} (${log.employeeId || 'N/A'})`;
                    row.insertCell().textContent = log.clientIpAddress || 'N/A';
                    
                    const detailsCell = row.insertCell();
                    if (log.detailsJson && log.detailsJson.trim() !== '' && log.detailsJson.trim() !== '{}') {
                        const detailsBtn = document.createElement('button');
                        detailsBtn.className = 'btn btn-sm btn-outline-info view-details-btn';
                        detailsBtn.innerHTML = '<i class="bi bi-info-circle"></i>';
                        detailsBtn.addEventListener('click', () => showLogDetails(log.detailsJson));
                        detailsCell.appendChild(detailsBtn);
                    } else {
                        detailsCell.textContent = '-';
                    }
                });
            } else {
                if(tableBody) tableBody.innerHTML = '<tr><td colspan="10" class="text-center">找不到符合條件的記錄</td></tr>';
            }
            if(paginationControls) renderPagination(pageData.page);
            // console.log("AUDIT_LOG_VIEW.JS: Audit logs table rendered.");
        } catch (error) {
            console.error("AUDIT_LOG_VIEW.JS: Error fetching audit logs:", error);
            if(tableBody) tableBody.innerHTML = `<tr><td colspan="10" class="text-danger text-center">載入操作歷程失敗: ${error.message}</td></tr>`;
            if(window.showToast) window.showToast('載入操作歷程失敗: ' + error.message, 'error');
        }
    }
    
    async function loadFilterDropdowns() {
        console.log("AUDIT_LOG_VIEW.JS: loadFilterDropdowns() called.");
        try {
            if(dataTypeSelect) {
                console.log("AUDIT_LOG_VIEW.JS: Fetching /api/v1/enums/audit-data-types");
                const dtResponse = await window.fetchAuthenticated('/api/v1/enums/audit-data-types');
                const dtApiResp = await dtResponse.json();
                if (dtResponse.ok && dtApiResp.data) {
                    populateSelect(dataTypeSelect, dtApiResp.data, false, "所有資料種類", "value", "label");
                } else {
                    console.error("AUDIT_LOG_VIEW.JS: Failed to load audit-data-types", dtApiResp);
                }
            }
        } catch (e) { console.error("AUDIT_LOG_VIEW.JS: Error loading data types for filter", e); }

        try {
            if(departmentSelect) {
                console.log("AUDIT_LOG_VIEW.JS: Fetching /api/v1/audit-logs/meta/department-names"); // Keeping this dynamic for now
                const deptResponse = await window.fetchAuthenticated('/api/v1/audit-logs/meta/department-names');
                const deptApiResp = await deptResponse.json();
                if (deptResponse.ok && deptApiResp.data) {
                     populateSelect(departmentSelect, deptApiResp.data, true, "所有部門"); 
                } else {
                    console.error("AUDIT_LOG_VIEW.JS: Failed to load department-names", deptApiResp);
                }
            }
        } catch (e) { console.error("AUDIT_LOG_VIEW.JS: Error loading departments for filter", e); }
        
         try {
            if(actionTypeSelect) {
                console.log("AUDIT_LOG_VIEW.JS: Fetching /api/v1/enums/audit-action-types");
                const atResponse = await window.fetchAuthenticated('/api/v1/enums/audit-action-types');
                const atApiResp = await atResponse.json();
                if (atResponse.ok && atApiResp.data) {
                    populateSelect(actionTypeSelect, atApiResp.data, false, "所有操作行為", "value", "label");
                } else {
                    console.error("AUDIT_LOG_VIEW.JS: Failed to load audit-action-types", atApiResp);
                }
            }
        } catch (e) { console.error("AUDIT_LOG_VIEW.JS: Error loading action types for filter", e); }
        console.log("AUDIT_LOG_VIEW.JS: Filter dropdowns population attempt complete.");
    }

    function populateSelect(selectElement, optionsArray, isValueAlsoText = true, defaultOptionText = "全部", valueField = null, textField = null) {
        if (!selectElement) return;
        selectElement.innerHTML = `<option value="">${defaultOptionText}</option>`;
        optionsArray.forEach(opt => {
            const option = document.createElement('option');
            if (isValueAlsoText && typeof opt === 'string') { 
                option.value = opt;
                option.textContent = opt;
            } else if (!isValueAlsoText && typeof opt === 'object' && opt !== null && valueField && textField) {
                option.value = opt[valueField];
                option.textContent = opt[textField];
            } else {
                console.warn("AUDIT_LOG_VIEW.JS: Invalid option for populateSelect", opt);
                return; 
            }
            selectElement.appendChild(option);
        });
    }

    if(filterForm) {
        filterForm.addEventListener('submit', function(event) {
            event.preventDefault();
            console.log("AUDIT_LOG_VIEW.JS: Filter form submitted.");
            fetchAuditLogs(0, currentSortBy, currentSortDir);
        });
    }
    
    document.querySelectorAll('th[data-sort]').forEach(th => {
        th.addEventListener('click', function() {
            const newSortBy = this.dataset.sort;
            let newSortDir = 'ASC';
            if (newSortBy === currentSortBy && currentSortDir === 'ASC') {
                newSortDir = 'DESC';
            }
            document.querySelectorAll('th[data-sort] i').forEach(i => i.className = 'bi bi-arrow-down-up');
            const icon = this.querySelector('i');
            if(icon) icon.className = newSortDir === 'ASC' ? 'bi bi-sort-up' : 'bi bi-sort-down';
            
            fetchAuditLogs(currentPage, newSortBy, newSortDir);
        });
    });

    function renderPagination(page) {
        if (!paginationControls || !page) return;
        paginationControls.innerHTML = '';
        if (page.totalPages <= 1) return;

        const ul = document.createElement('ul');
        ul.className = 'pagination justify-content-center';

        addPageItem(ul, '«', 0, page.page <= 0);
        addPageItem(ul, '‹', page.page - 1, page.page <= 0);
        
        const startPage = Math.max(0, page.page - 2);
        const endPage = Math.min(page.totalPages - 1, page.page + 2);

        if (startPage > 0) addPageItem(ul, '...', -1, true); 

        for (let i = startPage; i <= endPage; i++) {
            addPageItem(ul, i + 1, i, false, i === page.page);
        }

        if (endPage < page.totalPages - 1) addPageItem(ul, '...', -1, true); 

        addPageItem(ul, '›', page.page + 1, page.page >= page.totalPages - 1);
        addPageItem(ul, '»', page.totalPages - 1, page.page >= page.totalPages - 1);
        
        paginationControls.appendChild(ul);
    }

    function addPageItem(ul, text, pageNum, isDisabled = false, isActive = false) {
        const li = document.createElement('li');
        li.className = `page-item ${isDisabled ? 'disabled' : ''} ${isActive ? 'active' : ''}`;
        const a = document.createElement('a');
        a.className = 'page-link';
        a.href = '#';
        a.textContent = text;
        if (!isDisabled && pageNum !== -1) { 
            a.addEventListener('click', (e) => {
                e.preventDefault();
                fetchAuditLogs(pageNum, currentSortBy, currentSortDir);
            });
        }
        li.appendChild(a);
        ul.appendChild(li);
    }
    
    function showLogDetails(details) {
        if (!logDetailsJsonCode || !detailsModal) return;
        try {
            const parsedJson = JSON.parse(details);
            logDetailsJsonCode.textContent = JSON.stringify(parsedJson, null, 2);
        } catch (e) {
            logDetailsJsonCode.textContent = details; 
        }
        detailsModal.show();
    }

    console.log("AUDIT_LOG_VIEW.JS: Initializing page...");
    loadFilterDropdowns();
    fetchAuditLogs(); 
    console.log("AUDIT_LOG_VIEW.JS: Page initialization sequence started.");
}); 